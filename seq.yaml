# general
gpu_id: 0
use_gpu: True
seed: 2020
state: INFO
reproducibility: True
checkpoint_dir: './log/Checkpoint'
show_progress: True

# Yelp, Amazon
field_separator: "\t"
seq_separator: " "
USER_ID_FIELD: user_id
ITEM_ID_FIELD: item_id
load_col:
    inter: [user_id, item_id]

NEG_PREFIX: neg_
LABEL_FIELD: label
ITEM_LIST_LENGTH_FIELD: item_length
LIST_SUFFIX: _list
MAX_ITEM_LIST_LENGTH: 50
POSITION_FIELD: position_id

#max_user_inter_num: 100
min_user_inter_num: 5
#max_item_inter_num: 100
min_item_inter_num: 5

# training settings
epochs: 50
train_batch_size: 256
learner: adam
learning_rate: 0.001
training_neg_sample_num: 0
eval_step: 1
stopping_step: 10
clip_grad_norm: ~
# clip_grad_norm:  {'max_norm': 5, 'norm_type': 2}
draw_loss_pic: False

# evalution settings
eval_setting: TO_LS,full
metrics: ["Recall", "MRR","NDCG","Precision"]
valid_metric: MRR@10
eval_batch_size: 256
weight_decay: 0
topk: [5, 10, 20, 50]
group_by_user: True
leave_one_num: 2
real_time_process: False
valid_metric_bigger: True
loss_decimal_place: 4
metric_decimal_place: 4


# choose from {dot, cos}
sim: 'dot'
tau: 1
# directory setting
log_root: "./log/"
data_path: "./dataset/"

hidden_dropout_prob: 0.5
attn_dropout_prob: 0.5