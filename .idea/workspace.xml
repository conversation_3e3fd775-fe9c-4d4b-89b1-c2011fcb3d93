<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="6309c5c9-66c0-430e-adea-73d5846e10d1" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/recbole/data/dataloader/sequential_dataloader.py" beforeDir="false" afterPath="$PROJECT_DIR$/recbole/data/dataloader/sequential_dataloader.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/recbole/model/extractors.py" beforeDir="false" afterPath="$PROJECT_DIR$/recbole/model/extractors.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/recbole/model/sequential_recommender/mclrec.py" beforeDir="false" afterPath="$PROJECT_DIR$/recbole/model/sequential_recommender/mclrec.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectId" id="2FymrkmD9PySZ4AoSCtBWxyOh5K" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="settings.editor.selected.configurable" value="ssh.settings" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="6309c5c9-66c0-430e-adea-73d5846e10d1" name="Changes" comment="" />
      <created>1665472899121</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1665472899121</updated>
      <workItem from="1665472900990" duration="758000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>