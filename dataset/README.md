## Datasets

We provide four preprocessed datasets, Beauty, Sports_and_Outdoors, Toys_and_Games, Yelp and ML-1M.

- The first three datasets are originally from [here](http://jmcauley.ucsd.edu/data/amazon/index.html).

- Cite following one or both if you use them:

```
@inproceedings{he2016ups,
  title={Ups and downs: Modeling the visual evolution of fashion trends with one-class collaborative filtering},
  author={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON>},
  booktitle={proceedings of the 25th international conference on world wide web},
  pages={507--517},
  year={2016}
}
```
- and
```
@inproceedings{mcauley2015image,
  title={Image-based recommendations on styles and substitutes},
  author={<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle={Proceedings of the 38th international ACM SIGIR conference on research and development in information retrieval},
  pages={43--52},
  year={2015}
}
```

- The Yelp dataset is from [https://www.yelp.com/dataset.](https://www.yelp.com/dataset)

- The ML-1M dataset is from [https://grouplens.org/datasets/movielens/1m/](https://grouplens.org/datasets/movielens/1m/).

We used [RecSysDatsets](https://github.com/RUCAIBox/RecSysDatasets) for processing the training data.



