Wed 23 Jul 2025 15:30:25 INFO  
[1;35mGeneral Hyper Parameters:
[0m[1;36mgpu_id[0m =[1;33m 0[0m
[1;36muse_gpu[0m =[1;33m True[0m
[1;36mseed[0m =[1;33m 2020[0m
[1;36mstate[0m =[1;33m INFO[0m
[1;36mreproducibility[0m =[1;33m True[0m
[1;36mshow_progress[0m =[1;33m True[0m
[1;36mdata_path[0m =[1;33m ./dataset/mooc[0m

[1;35mTraining Hyper Parameters:
[0m[1;36mcheckpoint_dir[0m =[1;33m ./log/Checkpoint[0m
[1;36mepochs[0m =[1;33m 50[0m
[1;36mtrain_batch_size[0m =[1;33m 256[0m
[1;36mlearner[0m =[1;33m adam[0m
[1;36mlearning_rate[0m =[1;33m 0.001[0m
[1;36mtraining_neg_sample_num[0m =[1;33m 0[0m
[1;36meval_step[0m =[1;33m 1[0m
[1;36mstopping_step[0m =[1;33m 10[0m
[1;36mclip_grad_norm[0m =[1;33m None[0m
[1;36mdraw_loss_pic[0m =[1;33m False[0m
[1;36mweight_decay[0m =[1;33m 0[0m
[1;36mloss_decimal_place[0m =[1;33m 4[0m

[1;35mEvaluation Hyper Parameters:
[0m[1;36meval_setting[0m =[1;33m TO_LS,full[0m
[1;36mmetrics[0m =[1;33m ['Recall', 'MRR', 'NDCG', 'Precision'][0m
[1;36mvalid_metric[0m =[1;33m MRR@10[0m
[1;36meval_batch_size[0m =[1;33m 256[0m
[1;36mtopk[0m =[1;33m [5, 10, 20, 50][0m
[1;36mgroup_by_user[0m =[1;33m True[0m
[1;36mleave_one_num[0m =[1;33m 2[0m
[1;36mreal_time_process[0m =[1;33m False[0m
[1;36mmetric_decimal_place[0m =[1;33m 4[0m

[1;35mDataset Hyper Parameters:
[0m[1;36mfield_separator[0m =[1;33m 	[0m
[1;36mseq_separator[0m =[1;33m  [0m
[1;36mUSER_ID_FIELD[0m =[1;33m user_id[0m
[1;36mITEM_ID_FIELD[0m =[1;33m item_id[0m
[1;36mload_col[0m =[1;33m {'inter': ['user_id', 'item_id']}[0m
[1;36mNEG_PREFIX[0m =[1;33m neg_[0m
[1;36mLABEL_FIELD[0m =[1;33m label[0m
[1;36mITEM_LIST_LENGTH_FIELD[0m =[1;33m item_length[0m
[1;36mLIST_SUFFIX[0m =[1;33m _list[0m
[1;36mMAX_ITEM_LIST_LENGTH[0m =[1;33m 50[0m
[1;36mPOSITION_FIELD[0m =[1;33m position_id[0m
[1;36mmin_user_inter_num[0m =[1;33m 5[0m
[1;36mmin_item_inter_num[0m =[1;33m 5[0m

[1;35mOther Hyper Parameters: 
[0m[1;36mn_layers[0m = [1;33m2[0m
[1;36mn_heads[0m = [1;33m2[0m
[1;36mhidden_size[0m = [1;33m64[0m
[1;36minner_size[0m = [1;33m256[0m
[1;36mhidden_dropout_prob[0m = [1;33m0.5[0m
[1;36mattn_dropout_prob[0m = [1;33m0.5[0m
[1;36mhidden_act[0m = [1;33mgelu[0m
[1;36mlayer_norm_eps[0m = [1;33m1e-12[0m
[1;36minitializer_range[0m = [1;33m0.02[0m
[1;36mloss_type[0m = [1;33mCE[0m
[1;36mmask_ratio[0m = [1;33m0.5[0m
[1;36mcrop_ratio[0m = [1;33m0.5[0m
[1;36mreorder_ratio[0m = [1;33m0.8[0m
[1;36mlmd[0m = [1;33m0.03[0m
[1;36mbeta[0m = [1;33m0.1[0m
[1;36muse_rl[0m = [1;33m1[0m
[1;36mjoint[0m = [1;33m0[0m
[1;36mSSL_AUG[0m = [1;33mMCLRec[0m
[1;36mMODEL_TYPE[0m = [1;33mModelType.SEQUENTIAL[0m
[1;36mvalid_metric_bigger[0m = [1;33mTrue[0m
[1;36msim[0m = [1;33mdot[0m
[1;36mtau[0m = [1;33m1[0m
[1;36mlog_root[0m = [1;33m./log/[0m
[1;36mMODEL_INPUT_TYPE[0m = [1;33mInputType.POINTWISE[0m
[1;36meval_type[0m = [1;33mEvaluatorType.RANKING[0m
[1;36mdevice[0m = [1;33mmps[0m
[1;36mtrain_neg_sample_args[0m = [1;33m{'strategy': 'none'}[0m
[1;36mlog_dir[0m = [1;33m/Users/<USER>/code/recbole/MCLRec/log/MCLRec/mooc/bs256-lmd0.03-beta0.1-Jul-23-2025_15-30-25[0m


Wed 23 Jul 2025 15:30:25 INFO  [1;35mmooc[0m
[1;34mThe number of users[0m: 35619
[1;34mAverage actions of users[0m: 8.45229939918019
[1;34mThe number of items[0m: 768
[1;34mAverage actions of items[0m: 392.50847457627117
[1;34mThe number of inters[0m: 301054
[1;34mThe sparsity of the dataset[0m: 98.89947108050947%
[1;34mRemain Fields[0m: ['user_id', 'item_id']
Wed 23 Jul 2025 15:30:25 INFO  [1;35mBuild[0m[1;33m [SequentialDataLoader][0m for [1;33m[train][0m with format [1;33m[InputType.POINTWISE][0m
Wed 23 Jul 2025 15:30:25 INFO  [1;35m[train][0m[1;33m No Negative Sampling[0m
Wed 23 Jul 2025 15:30:25 INFO  [1;35m[train][0m[1;36m batch_size[0m = [1;33m[256][0m, [1;36mshuffle[0m = [1;33m[True]
[0m
Wed 23 Jul 2025 15:30:26 INFO  [1;35mBuild[0m[1;33m [SequentialFullDataLoader][0m for [1;33m[evaluation][0m with format [1;33m[InputType.POINTWISE][0m
Wed 23 Jul 2025 15:30:26 INFO  [1;35mEvaluation Setting:[0m
	[1;34mGroup by[0m user_id
	[1;34mOrdering[0m: {'strategy': 'by', 'field': None, 'ascending': True}
	[1;34mSplitting[0m: {'strategy': 'loo', 'leave_one_num': 2}
	[1;34mNegative Sampling[0m: {'strategy': 'full', 'distribution': 'uniform'}
Wed 23 Jul 2025 15:30:26 INFO  [1;35m[evaluation][0m[1;36m batch_size[0m = [1;33m[256][0m, [1;36mshuffle[0m = [1;33m[False]
[0m
Wed 23 Jul 2025 15:30:26 INFO  MCLRec(
  (item_embedding): Embedding(769, 64, padding_idx=0)
  (position_embedding): Embedding(50, 64)
  (trm_encoder): TransformerEncoder(
    (layer): ModuleList(
      (0-1): 2 x TransformerLayer(
        (multi_head_attention): MultiHeadAttention(
          (query): Linear(in_features=64, out_features=64, bias=True)
          (key): Linear(in_features=64, out_features=64, bias=True)
          (value): Linear(in_features=64, out_features=64, bias=True)
          (attn_dropout): Dropout(p=0.5, inplace=False)
          (dense): Linear(in_features=64, out_features=64, bias=True)
          (LayerNorm): LayerNorm((64,), eps=1e-12, elementwise_affine=True)
          (out_dropout): Dropout(p=0.5, inplace=False)
        )
        (feed_forward): FeedForward(
          (dense_1): Linear(in_features=64, out_features=256, bias=True)
          (dense_2): Linear(in_features=256, out_features=64, bias=True)
          (LayerNorm): LayerNorm((64,), eps=1e-12, elementwise_affine=True)
          (dropout): Dropout(p=0.5, inplace=False)
        )
      )
    )
  )
  (LayerNorm): LayerNorm((64,), eps=1e-12, elementwise_affine=True)
  (dropout): Dropout(p=0.5, inplace=False)
  (loss_fct): CrossEntropyLoss()
  (nce_fct): CrossEntropyLoss()
)[1;34m
Trainable parameters[0m: 152512
Wed 23 Jul 2025 15:34:46 INFO  [1;32mepoch 0 training[0m [[1;34mtime[0m: 259.73s, [1;34mtrain loss[0m: 28455.4970]
Wed 23 Jul 2025 15:34:48 INFO  [1;32mepoch 0 evaluating[0m [[1;34mtime[0m: 1.78s, [1;34mvalid_score[0m: 0.156600]
Wed 23 Jul 2025 15:34:48 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3039    recall@10 : 0.3781    recall@20 : 0.4655    recall@50 : 0.6162    mrr@5 : 0.1468    mrr@10 : 0.1566    mrr@20 : 0.1626    mrr@50 : 0.1673    ndcg@5 : 0.1859    ndcg@10 : 0.2098    ndcg@20 : 0.2318    ndcg@50 : 0.2615    precision@5 : 0.0608    precision@10 : 0.0378    precision@20 : 0.0233    precision@50 : 0.0123    
Wed 23 Jul 2025 15:34:48 INFO  [1;34mSaving current best[0m: /Users/<USER>/code/recbole/MCLRec/log/MCLRec/mooc/bs256-lmd0.03-beta0.1-Jul-23-2025_15-30-25/model.pth
Wed 23 Jul 2025 15:39:05 INFO  [1;32mepoch 1 training[0m [[1;34mtime[0m: 256.66s, [1;34mtrain loss[0m: 23595.2394]
Wed 23 Jul 2025 15:39:06 INFO  [1;32mepoch 1 evaluating[0m [[1;34mtime[0m: 1.72s, [1;34mvalid_score[0m: 0.177900]
Wed 23 Jul 2025 15:39:06 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3147    recall@10 : 0.3995    recall@20 : 0.494    recall@50 : 0.6562    mrr@5 : 0.1665    mrr@10 : 0.1779    mrr@20 : 0.1844    mrr@50 : 0.1895    ndcg@5 : 0.2038    ndcg@10 : 0.2313    ndcg@20 : 0.2551    ndcg@50 : 0.2872    precision@5 : 0.0629    precision@10 : 0.0399    precision@20 : 0.0247    precision@50 : 0.0131    
Wed 23 Jul 2025 15:39:06 INFO  [1;34mSaving current best[0m: /Users/<USER>/code/recbole/MCLRec/log/MCLRec/mooc/bs256-lmd0.03-beta0.1-Jul-23-2025_15-30-25/model.pth
Wed 23 Jul 2025 15:43:26 INFO  [1;32mepoch 2 training[0m [[1;34mtime[0m: 259.15s, [1;34mtrain loss[0m: 22901.5692]
Wed 23 Jul 2025 15:43:27 INFO  [1;32mepoch 2 evaluating[0m [[1;34mtime[0m: 1.71s, [1;34mvalid_score[0m: 0.227100]
Wed 23 Jul 2025 15:43:27 INFO  [1;34mvalid result[0m: 
recall@5 : 0.32    recall@10 : 0.4097    recall@20 : 0.5121    recall@50 : 0.6735    mrr@5 : 0.215    mrr@10 : 0.2271    mrr@20 : 0.2342    mrr@50 : 0.2393    ndcg@5 : 0.241    ndcg@10 : 0.2701    ndcg@20 : 0.2959    ndcg@50 : 0.328    precision@5 : 0.064    precision@10 : 0.041    precision@20 : 0.0256    precision@50 : 0.0135    
Wed 23 Jul 2025 15:43:27 INFO  [1;34mSaving current best[0m: /Users/<USER>/code/recbole/MCLRec/log/MCLRec/mooc/bs256-lmd0.03-beta0.1-Jul-23-2025_15-30-25/model.pth
Wed 23 Jul 2025 15:47:44 INFO  [1;32mepoch 3 training[0m [[1;34mtime[0m: 257.20s, [1;34mtrain loss[0m: 22613.7542]
Wed 23 Jul 2025 15:47:46 INFO  [1;32mepoch 3 evaluating[0m [[1;34mtime[0m: 1.59s, [1;34mvalid_score[0m: 0.226600]
Wed 23 Jul 2025 15:47:46 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3234    recall@10 : 0.4169    recall@20 : 0.5209    recall@50 : 0.6836    mrr@5 : 0.2139    mrr@10 : 0.2266    mrr@20 : 0.2337    mrr@50 : 0.2389    ndcg@5 : 0.241    ndcg@10 : 0.2715    ndcg@20 : 0.2977    ndcg@50 : 0.33    precision@5 : 0.0647    precision@10 : 0.0417    precision@20 : 0.026    precision@50 : 0.0137    
Wed 23 Jul 2025 15:52:03 INFO  [1;32mepoch 4 training[0m [[1;34mtime[0m: 257.24s, [1;34mtrain loss[0m: 22454.0293]
Wed 23 Jul 2025 15:52:05 INFO  [1;32mepoch 4 evaluating[0m [[1;34mtime[0m: 1.58s, [1;34mvalid_score[0m: 0.231100]
Wed 23 Jul 2025 15:52:05 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3347    recall@10 : 0.421    recall@20 : 0.5284    recall@50 : 0.6881    mrr@5 : 0.2196    mrr@10 : 0.2311    mrr@20 : 0.2385    mrr@50 : 0.2436    ndcg@5 : 0.248    ndcg@10 : 0.2759    ndcg@20 : 0.3029    ndcg@50 : 0.3347    precision@5 : 0.0669    precision@10 : 0.0421    precision@20 : 0.0264    precision@50 : 0.0138    
Wed 23 Jul 2025 15:52:05 INFO  [1;34mSaving current best[0m: /Users/<USER>/code/recbole/MCLRec/log/MCLRec/mooc/bs256-lmd0.03-beta0.1-Jul-23-2025_15-30-25/model.pth
Wed 23 Jul 2025 15:56:23 INFO  [1;32mepoch 5 training[0m [[1;34mtime[0m: 257.66s, [1;34mtrain loss[0m: 22356.4799]
Wed 23 Jul 2025 15:56:24 INFO  [1;32mepoch 5 evaluating[0m [[1;34mtime[0m: 1.63s, [1;34mvalid_score[0m: 0.228300]
Wed 23 Jul 2025 15:56:24 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3363    recall@10 : 0.4274    recall@20 : 0.5323    recall@50 : 0.6911    mrr@5 : 0.2163    mrr@10 : 0.2283    mrr@20 : 0.2355    mrr@50 : 0.2406    ndcg@5 : 0.2459    ndcg@10 : 0.2752    ndcg@20 : 0.3016    ndcg@50 : 0.3332    precision@5 : 0.0673    precision@10 : 0.0427    precision@20 : 0.0266    precision@50 : 0.0138    
Wed 23 Jul 2025 16:00:52 INFO  [1;32mepoch 6 training[0m [[1;34mtime[0m: 268.05s, [1;34mtrain loss[0m: 22287.2785]
Wed 23 Jul 2025 16:00:54 INFO  [1;32mepoch 6 evaluating[0m [[1;34mtime[0m: 1.63s, [1;34mvalid_score[0m: 0.232800]
Wed 23 Jul 2025 16:00:54 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3411    recall@10 : 0.4284    recall@20 : 0.5363    recall@50 : 0.6956    mrr@5 : 0.2213    mrr@10 : 0.2328    mrr@20 : 0.2402    mrr@50 : 0.2453    ndcg@5 : 0.2508    ndcg@10 : 0.2789    ndcg@20 : 0.3061    ndcg@50 : 0.3377    precision@5 : 0.0682    precision@10 : 0.0428    precision@20 : 0.0268    precision@50 : 0.0139    
Wed 23 Jul 2025 16:00:54 INFO  [1;34mSaving current best[0m: /Users/<USER>/code/recbole/MCLRec/log/MCLRec/mooc/bs256-lmd0.03-beta0.1-Jul-23-2025_15-30-25/model.pth
Wed 23 Jul 2025 16:05:16 INFO  [1;32mepoch 7 training[0m [[1;34mtime[0m: 261.94s, [1;34mtrain loss[0m: 22236.4575]
Wed 23 Jul 2025 16:05:18 INFO  [1;32mepoch 7 evaluating[0m [[1;34mtime[0m: 1.70s, [1;34mvalid_score[0m: 0.238000]
Wed 23 Jul 2025 16:05:18 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3406    recall@10 : 0.4297    recall@20 : 0.5369    recall@50 : 0.6971    mrr@5 : 0.2263    mrr@10 : 0.238    mrr@20 : 0.2455    mrr@50 : 0.2506    ndcg@5 : 0.2546    ndcg@10 : 0.2833    ndcg@20 : 0.3104    ndcg@50 : 0.3421    precision@5 : 0.0681    precision@10 : 0.043    precision@20 : 0.0268    precision@50 : 0.0139    
Wed 23 Jul 2025 16:05:18 INFO  [1;34mSaving current best[0m: /Users/<USER>/code/recbole/MCLRec/log/MCLRec/mooc/bs256-lmd0.03-beta0.1-Jul-23-2025_15-30-25/model.pth
Wed 23 Jul 2025 16:09:36 INFO  [1;32mepoch 8 training[0m [[1;34mtime[0m: 258.34s, [1;34mtrain loss[0m: 22193.0728]
Wed 23 Jul 2025 16:09:38 INFO  [1;32mepoch 8 evaluating[0m [[1;34mtime[0m: 2.24s, [1;34mvalid_score[0m: 0.241700]
Wed 23 Jul 2025 16:09:38 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3424    recall@10 : 0.4324    recall@20 : 0.5414    recall@50 : 0.6998    mrr@5 : 0.2298    mrr@10 : 0.2417    mrr@20 : 0.2492    mrr@50 : 0.2543    ndcg@5 : 0.2576    ndcg@10 : 0.2867    ndcg@20 : 0.3142    ndcg@50 : 0.3456    precision@5 : 0.0685    precision@10 : 0.0432    precision@20 : 0.0271    precision@50 : 0.014    
Wed 23 Jul 2025 16:09:38 INFO  [1;34mSaving current best[0m: /Users/<USER>/code/recbole/MCLRec/log/MCLRec/mooc/bs256-lmd0.03-beta0.1-Jul-23-2025_15-30-25/model.pth
Wed 23 Jul 2025 16:14:01 INFO  [1;32mepoch 9 training[0m [[1;34mtime[0m: 263.24s, [1;34mtrain loss[0m: 22159.3261]
Wed 23 Jul 2025 16:14:03 INFO  [1;32mepoch 9 evaluating[0m [[1;34mtime[0m: 1.60s, [1;34mvalid_score[0m: 0.237200]
Wed 23 Jul 2025 16:14:03 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3393    recall@10 : 0.4345    recall@20 : 0.5414    recall@50 : 0.7003    mrr@5 : 0.2246    mrr@10 : 0.2372    mrr@20 : 0.2446    mrr@50 : 0.2497    ndcg@5 : 0.253    ndcg@10 : 0.2837    ndcg@20 : 0.3107    ndcg@50 : 0.3422    precision@5 : 0.0679    precision@10 : 0.0434    precision@20 : 0.0271    precision@50 : 0.014    
Wed 23 Jul 2025 16:18:16 INFO  [1;32mepoch 10 training[0m [[1;34mtime[0m: 252.85s, [1;34mtrain loss[0m: 22132.1896]
Wed 23 Jul 2025 16:18:17 INFO  [1;32mepoch 10 evaluating[0m [[1;34mtime[0m: 1.62s, [1;34mvalid_score[0m: 0.240400]
Wed 23 Jul 2025 16:18:17 INFO  [1;34mvalid result[0m: 
recall@5 : 0.342    recall@10 : 0.432    recall@20 : 0.542    recall@50 : 0.7005    mrr@5 : 0.2285    mrr@10 : 0.2404    mrr@20 : 0.248    mrr@50 : 0.253    ndcg@5 : 0.2566    ndcg@10 : 0.2856    ndcg@20 : 0.3134    ndcg@50 : 0.3448    precision@5 : 0.0684    precision@10 : 0.0432    precision@20 : 0.0271    precision@50 : 0.014    
Wed 23 Jul 2025 16:22:38 INFO  [1;32mepoch 11 training[0m [[1;34mtime[0m: 260.52s, [1;34mtrain loss[0m: 22108.3905]
Wed 23 Jul 2025 16:22:40 INFO  [1;32mepoch 11 evaluating[0m [[1;34mtime[0m: 1.69s, [1;34mvalid_score[0m: 0.236500]
Wed 23 Jul 2025 16:22:40 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3426    recall@10 : 0.4358    recall@20 : 0.5437    recall@50 : 0.7021    mrr@5 : 0.2242    mrr@10 : 0.2365    mrr@20 : 0.244    mrr@50 : 0.249    ndcg@5 : 0.2535    ndcg@10 : 0.2835    ndcg@20 : 0.3107    ndcg@50 : 0.3422    precision@5 : 0.0685    precision@10 : 0.0436    precision@20 : 0.0272    precision@50 : 0.014    
Wed 23 Jul 2025 16:26:58 INFO  [1;32mepoch 12 training[0m [[1;34mtime[0m: 258.72s, [1;34mtrain loss[0m: 22083.9877]
Wed 23 Jul 2025 16:27:00 INFO  [1;32mepoch 12 evaluating[0m [[1;34mtime[0m: 1.66s, [1;34mvalid_score[0m: 0.235800]
Wed 23 Jul 2025 16:27:00 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3433    recall@10 : 0.4375    recall@20 : 0.5441    recall@50 : 0.7037    mrr@5 : 0.2233    mrr@10 : 0.2358    mrr@20 : 0.2431    mrr@50 : 0.2482    ndcg@5 : 0.2529    ndcg@10 : 0.2833    ndcg@20 : 0.3102    ndcg@50 : 0.3419    precision@5 : 0.0687    precision@10 : 0.0438    precision@20 : 0.0272    precision@50 : 0.0141    
Wed 23 Jul 2025 16:31:17 INFO  [1;32mepoch 13 training[0m [[1;34mtime[0m: 256.65s, [1;34mtrain loss[0m: 22070.9083]
Wed 23 Jul 2025 16:31:18 INFO  [1;32mepoch 13 evaluating[0m [[1;34mtime[0m: 1.70s, [1;34mvalid_score[0m: 0.241000]
Wed 23 Jul 2025 16:31:18 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3448    recall@10 : 0.4365    recall@20 : 0.5437    recall@50 : 0.7013    mrr@5 : 0.229    mrr@10 : 0.241    mrr@20 : 0.2484    mrr@50 : 0.2535    ndcg@5 : 0.2576    ndcg@10 : 0.2871    ndcg@20 : 0.3142    ndcg@50 : 0.3455    precision@5 : 0.069    precision@10 : 0.0436    precision@20 : 0.0272    precision@50 : 0.014    
Wed 23 Jul 2025 16:35:39 INFO  [1;32mepoch 14 training[0m [[1;34mtime[0m: 260.60s, [1;34mtrain loss[0m: 22050.9644]
Wed 23 Jul 2025 16:35:41 INFO  [1;32mepoch 14 evaluating[0m [[1;34mtime[0m: 1.60s, [1;34mvalid_score[0m: 0.243900]
Wed 23 Jul 2025 16:35:41 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3439    recall@10 : 0.4359    recall@20 : 0.5427    recall@50 : 0.7029    mrr@5 : 0.2317    mrr@10 : 0.2439    mrr@20 : 0.2513    mrr@50 : 0.2565    ndcg@5 : 0.2594    ndcg@10 : 0.2891    ndcg@20 : 0.3161    ndcg@50 : 0.348    precision@5 : 0.0688    precision@10 : 0.0436    precision@20 : 0.0271    precision@50 : 0.0141    
Wed 23 Jul 2025 16:35:41 INFO  [1;34mSaving current best[0m: /Users/<USER>/code/recbole/MCLRec/log/MCLRec/mooc/bs256-lmd0.03-beta0.1-Jul-23-2025_15-30-25/model.pth
Wed 23 Jul 2025 16:39:58 INFO  [1;32mepoch 15 training[0m [[1;34mtime[0m: 257.51s, [1;34mtrain loss[0m: 22036.0171]
Wed 23 Jul 2025 16:40:00 INFO  [1;32mepoch 15 evaluating[0m [[1;34mtime[0m: 1.65s, [1;34mvalid_score[0m: 0.194100]
Wed 23 Jul 2025 16:40:00 INFO  [1;34mvalid result[0m: 
recall@5 : 0.347    recall@10 : 0.438    recall@20 : 0.5461    recall@50 : 0.7046    mrr@5 : 0.182    mrr@10 : 0.1941    mrr@20 : 0.2015    mrr@50 : 0.2066    ndcg@5 : 0.2234    ndcg@10 : 0.2528    ndcg@20 : 0.2801    ndcg@50 : 0.3115    precision@5 : 0.0694    precision@10 : 0.0438    precision@20 : 0.0273    precision@50 : 0.0141    
Wed 23 Jul 2025 16:44:18 INFO  [1;32mepoch 16 training[0m [[1;34mtime[0m: 258.38s, [1;34mtrain loss[0m: 22023.4513]
Wed 23 Jul 2025 16:44:20 INFO  [1;32mepoch 16 evaluating[0m [[1;34mtime[0m: 1.59s, [1;34mvalid_score[0m: 0.240700]
Wed 23 Jul 2025 16:44:20 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3474    recall@10 : 0.4395    recall@20 : 0.5443    recall@50 : 0.7027    mrr@5 : 0.2285    mrr@10 : 0.2407    mrr@20 : 0.248    mrr@50 : 0.253    ndcg@5 : 0.2579    ndcg@10 : 0.2877    ndcg@20 : 0.3141    ndcg@50 : 0.3456    precision@5 : 0.0695    precision@10 : 0.0439    precision@20 : 0.0272    precision@50 : 0.0141    
Wed 23 Jul 2025 16:48:41 INFO  [1;32mepoch 17 training[0m [[1;34mtime[0m: 261.64s, [1;34mtrain loss[0m: 22014.5210]
Wed 23 Jul 2025 16:48:43 INFO  [1;32mepoch 17 evaluating[0m [[1;34mtime[0m: 1.64s, [1;34mvalid_score[0m: 0.240900]
Wed 23 Jul 2025 16:48:43 INFO  [1;34mvalid result[0m: 
recall@5 : 0.3476    recall@10 : 0.4393    recall@20 : 0.5446    recall@50 : 0.7032    mrr@5 : 0.2288    mrr@10 : 0.2409    mrr@20 : 0.2481    mrr@50 : 0.2532    ndcg@5 : 0.2582    ndcg@10 : 0.2878    ndcg@20 : 0.3143    ndcg@50 : 0.3458    precision@5 : 0.0695    precision@10 : 0.0439    precision@20 : 0.0272    precision@50 : 0.0141    
